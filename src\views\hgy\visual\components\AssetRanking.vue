<template>
  <div class="asset-ranking">
    <!-- 背景 -->
    <div class="ranking-bg"></div>

    <!-- 内容 -->
    <div class="ranking-content">
      <!-- 总标题 -->
      <div class="main-title">资产处置数据排名</div>

      <!-- 资产处置溢价额排名 -->
      <div class="ranking-section">
        <div class="section-header">
          <div class="section-divider left"></div>
          <div class="section-title">资产处置溢价额排名</div>
          <div class="section-divider right"></div>
        </div>
        <div class="ranking-list">
          <div v-for="(item, index) in assetPremiumAmountData" :key="index" class="ranking-item">
            <!-- 奖牌图标 -->
            <div class="medal-icon" :class="`rank-${index + 1}`">
              <div class="medal-bg"></div>
              <span class="rank-number">{{ index + 1 }}</span>
            </div>

            <!-- 项目名称 -->
            <div class="project-name">{{ item.name }}</div>

            <!-- 进度条 -->
            <div class="progress-container">
              <div class="progress-bar">
                <div
                  class="progress-fill"
                  :style="{
                    width: animatedAssetPremiumAmountData[index] ? `${(item.value / maxAssetPremiumAmount) * 100}%` : '0%',
                    transitionDelay: `${index * 200}ms`,
                  }"
                >
                  <div class="progress-diamond"></div>
                </div>
              </div>
              <div class="progress-value">
                <span class="animated-number">{{ animatedAssetPremiumAmountValues[index] || 0 }}</span>
                <span class="progress-unit">万</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 资产处置溢价率排名 -->
      <div class="ranking-section">
        <div class="section-header width-limit">
          <div class="section-divider left"></div>
          <div class="section-title">资产处置溢价率排名</div>
          <div class="section-divider right"></div>
        </div>
        <div class="ranking-list">
          <div v-for="(item, index) in assetPremiumRateData" :key="index" class="ranking-item">
            <!-- 奖牌图标 -->
            <div class="medal-icon" :class="`rank-${index + 1}`">
              <div class="medal-bg"></div>
              <span class="rank-number">{{ index + 1 }}</span>
            </div>

            <!-- 项目名称 -->
            <div class="project-name">{{ item.name }}</div>

            <!-- 进度条 -->
            <div class="progress-container">
              <div class="progress-bar">
                <div
                  class="progress-fill"
                  :style="{
                    width: animatedAssetPremiumRateData[index] ? `${item.value}%` : '0%',
                    transitionDelay: `${index * 200}ms`,
                  }"
                >
                  <div class="progress-diamond"></div>
                </div>
              </div>
              <div class="progress-value">
                <span class="animated-number">{{ animatedAssetPremiumRateValues[index] || 0 }}</span>
                <span class="progress-unit">%</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { ref, computed, onMounted } from 'vue';

  // 模拟数据
  const assetPremiumAmountData = ref([
    { name: '资产处置项目A', value: 5969 },
    { name: '资产处置项目B', value: 4569 },
    { name: '资产处置项目C', value: 3969 },
    { name: '资产处置项目D', value: 2969 },
    { name: '资产处置项目E', value: 1969 },
    { name: '资产处置项目F', value: 969 },
    { name: '资产处置项目G', value: 869 },
  ]);

  const assetPremiumRateData = ref([
    { name: '资产处置项目A', value: 95.5 },
    { name: '资产处置项目B', value: 92.3 },
    { name: '资产处置项目C', value: 89.7 },
    { name: '资产处置项目D', value: 87.2 },
    { name: '资产处置项目E', value: 85.8 },
    { name: '资产处置项目F', value: 83.4 },
    { name: '资产处置项目G', value: 81.9 },
  ]);

  // 动画控制状态
  const animatedAssetPremiumAmountData = ref<boolean[]>(new Array(assetPremiumAmountData.value.length).fill(false));
  const animatedAssetPremiumRateData = ref<boolean[]>(new Array(assetPremiumRateData.value.length).fill(false));

  // 动画数值
  const animatedAssetPremiumAmountValues = ref<number[]>(new Array(assetPremiumAmountData.value.length).fill(0));
  const animatedAssetPremiumRateValues = ref<number[]>(new Array(assetPremiumRateData.value.length).fill(0));

  // 计算最大溢价额用于进度条比例
  const maxAssetPremiumAmount = computed(() => {
    return Math.max(...assetPremiumAmountData.value.map((item) => item.value));
  });

  // 数字动画函数
  const animateNumber = (startValue: number, endValue: number, duration: number, callback: (value: number) => void) => {
    const startTime = Date.now();
    const animate = () => {
      const elapsed = Date.now() - startTime;
      const progress = Math.min(elapsed / duration, 1);

      // 使用缓动函数
      const easeOutQuart = 1 - Math.pow(1 - progress, 4);
      const currentValue = startValue + (endValue - startValue) * easeOutQuart;

      callback(Math.round(currentValue * 10) / 10); // 保留一位小数

      if (progress < 1) {
        requestAnimationFrame(animate);
      }
    };
    requestAnimationFrame(animate);
  };

  // 启动动画
  const startAnimations = () => {
    // 延迟启动，让组件完全渲染
    setTimeout(() => {
      // 启动溢价额动画
      assetPremiumAmountData.value.forEach((item, index) => {
        setTimeout(() => {
          animatedAssetPremiumAmountData.value[index] = true;

          // 启动数字动画
          animateNumber(0, item.value, 2000, (value) => {
            animatedAssetPremiumAmountValues.value[index] = value;
          });
        }, index * 200);
      });

      // 启动溢价率动画
      assetPremiumRateData.value.forEach((item, index) => {
        setTimeout(() => {
          animatedAssetPremiumRateData.value[index] = true;

          // 启动数字动画
          animateNumber(0, item.value, 2000, (value) => {
            animatedAssetPremiumRateValues.value[index] = value;
          });
        }, index * 200);
      });
    }, 500); // 延迟500ms开始动画
  };

  onMounted(() => {
    startAnimations();
  });
</script>

<style lang="less" scoped>
  @import '../styles/fullScreen/AssetRanking.less';
  @import '../styles/notFullScreen/AssetRanking.less';
</style>
