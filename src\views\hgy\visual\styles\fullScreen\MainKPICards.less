.main-kpi-cards.fullscreen {
  position: relative;
  width: 664px;
  height: 151px;
  border-radius: 8px;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 60px;

  .cards-bg {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: url('https://huigupaimai.oss-cn-beijing.aliyuncs.com/temp/center-bg_1754987648914.png') no-repeat center center;
    background-size: cover;
    z-index: 1;

    /* &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: linear-gradient(135deg, rgba(18, 230, 219, 0.1) 0%, rgba(18, 230, 219, 0.05) 50%, rgba(18, 230, 219, 0.1) 100%);
      border: 1px solid rgba(18, 230, 219, 0.3);
      border-radius: 8px;
    } */
  }

  .cards-content {
    position: relative;
    width: 55%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: space-around;
    z-index: 2;
  }

  .kpi-group {
    display: flex;
    margin-left: 20px;
  }

  .card-label {
    color: #ffffff;
    font-size: 22px;
    font-family: 'YouSheBiaoTiHei';
    margin-bottom: 2px;
    opacity: 0.9;
  }

  .card-value {
    display: flex;
    align-items: baseline;
    gap: 4px;

    :deep(.count-to) {
      color: #fff;
      font-size: 26px;
      font-family: 'DIN Bold';
      text-shadow: 0 0 15px rgba(18, 230, 219, 0.6);
    }

    .card-unit {
      color: #ffffff;
      font-size: 12px;
      font-family: 'PingFang Bold';
      opacity: 0.8;
    }
  }
}
