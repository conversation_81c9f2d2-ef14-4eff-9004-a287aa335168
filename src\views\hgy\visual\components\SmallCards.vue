<template>
  <div class="small-cards">
    <div class="cards-row">
      <!-- 左侧5个卡片 -->
      <div class="cards-column">
        <div v-for="(item, index) in leftCards" :key="index" class="small-card">
          <div class="card-icon">
            <div class="icon-bg">
              <img :src="item.icon" alt="" width="22" height="22" />
            </div>
          </div>
          <div class="card-content">
            <div class="card-label">{{ item.label }}</div>
            <div class="card-value">
              <CountTo :start="0" :end="item.value" :duration="2000" :decimals="0" />
              <span class="card-unit">{{ item.unit }}</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 右侧5个卡片 -->
      <div class="cards-column">
        <div v-for="(item, index) in rightCards" :key="index" class="small-card">
          <div class="card-icon">
            <div class="icon-bg">
              <img :src="item.icon" alt="" width="22" height="22" />
            </div>
          </div>
          <div class="card-content">
            <div class="card-label">{{ item.label }}</div>
            <div class="card-value">
              <CountTo :start="0" :end="item.value" :duration="2000" :decimals="item.decimals || 0" />
              <span class="card-unit">{{ item.unit }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { ref } from 'vue';
  import CountTo from './CountTo.vue';
  import icon1 from '@/assets/visual/center/icon1.png';
  import icon2 from '@/assets/visual/center/icon2.png';
  import icon3 from '@/assets/visual/center/icon3.png';
  import icon4 from '@/assets/visual/center/icon4.png';
  import icon5 from '@/assets/visual/center/icon5.png';

  // 左侧卡片数据
  const leftCards = ref([
    { label: '标的总量', value: 5956, unit: '个', icon: icon1 },
    { label: '标的成交额', value: 35956, unit: '万', icon: icon2 },
    { label: '标的溢价率', value: 87.5, unit: '%', decimals: 2, icon: icon3 },
    { label: '成交量', value: 5956, unit: '个', icon: icon4 },
    { label: '标的溢价额', value: 5956, unit: '万', icon: icon5 },
  ]);

  // 右侧卡片数据
  const rightCards = ref([
    { label: '资产处置总量', value: 2956, unit: '个', icon: icon1 },
    { label: '资产处置成交额', value: 23689, unit: '万', icon: icon2 },
    { label: '资产处置溢价率', value: 95.23, unit: '%', decimals: 2, icon: icon3 },
    { label: '流拍量', value: 5956, unit: '个', icon: icon4 },
    { label: '资产处置溢价额', value: 5956, unit: '万', icon: icon5 },
  ]);
</script>

<style lang="less" scoped>
  @import '../styles/fullScreen/SmallCards.less';
  @import '../styles/notFullScreen/SmallCards.less';
</style>
