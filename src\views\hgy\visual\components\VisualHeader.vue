<template>
  <div class="visual-header">
    <!-- 头部内容 -->
    <div class="header-content">
      <!-- 左侧按钮 -->
      <div class="header-left">
        <!-- 物资种类选择 -->
        <div class="nav-button material-select">
          <a-cascader
            v-model:value="selectedMaterial"
            :options="materialTypeOptions"
            placeholder="请选择物资种类"
            show-search
            :field-names="{ label: 'name', value: 'id', children: 'children' }"
            change-on-select
            :filter-option="filterMaterialType"
            @change="handleMaterialChange"
            :dropdown-style="{ background: 'rgba(0, 76, 102, 0.8)', border: '1px solid #2BCCFF', backdropFilter: 'blur(10px)', color: '#fff' }"
            :dropdown-class-name="'visual-cascader-dropdown'"
          >
            <div class="button-text">{{ selectedMaterialText || '请选择物资种类' }}</div>
          </a-cascader>
        </div>
        <!-- 省市区选择 -->
        <div class="nav-button address-select">
          <a-cascader
            v-model:value="selectedArea"
            :options="areaOptions"
            placeholder="选择省市区"
            @change="handleAreaChange"
            :dropdown-style="{ background: 'rgba(0, 76, 102, 0.8)', border: '1px solid #2BCCFF', backdropFilter: 'blur(10px)', color: '#fff' }"
            :dropdown-class-name="'visual-cascader-dropdown'"
          >
            <div class="button-text">{{ selectedAreaText || '选择省市区' }}</div>
          </a-cascader>
        </div>
      </div>

      <!-- 中间标题 -->
      <div class="header-center">
        <h1 class="main-title">灰谷网经营数据可视化大屏</h1>
      </div>

      <!-- 右侧信息 -->
      <div class="header-right">
        <div class="time-info">{{ currentTime }}</div>
        <!-- 暂时隐藏返回灰谷云按钮 -->
        <!-- <div class="nav-button return-button">
          <span class="button-text">返回灰谷云</span>
        </div> -->
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { ref, onMounted, onUnmounted, computed } from 'vue';
  import dayjs from 'dayjs';
  import { getMaterialTree } from '@/api/supplyAndDemand/SupplyDemand';
  import type { MaterialTypeNode } from '@/api/supplyAndDemand/SupplyDemand';
  import { regionData } from '/@/components/Form/src/utils/areaDataUtil';

  // 定义事件
  const emit = defineEmits<{
    materialChange: [value: string[], text: string];
    areaChange: [value: string[], text: string];
  }>();

  const currentTime = ref('');

  // 物资种类相关
  const selectedMaterial = ref<string[]>([]);
  const materialTypeOptions = ref<MaterialTypeNode[]>([]);

  // 省市区相关
  const selectedArea = ref<string[]>([]);
  const areaOptions = ref(regionData);

  // 计算显示文本
  const selectedMaterialText = computed(() => {
    if (selectedMaterial.value && selectedMaterial.value.length > 0) {
      // 只显示最后一级的名称
      const findLastLevelName = (options: MaterialTypeNode[], ids: string[], currentIndex = 0): string => {
        if (!ids || ids.length === 0) return '';

        for (const option of options) {
          if (option.id === ids[currentIndex]) {
            // 如果这是最后一级，返回当前名称
            if (currentIndex === ids.length - 1) {
              return option.name;
            }
            // 如果还有下一级，继续查找
            if (option.children && currentIndex < ids.length - 1) {
              return findLastLevelName(option.children, ids, currentIndex + 1);
            }
            // 如果没有子级但还有ID，返回当前名称
            return option.name;
          }
        }
        return '';
      };

      return findLastLevelName(materialTypeOptions.value, selectedMaterial.value);
    }
    return '';
  });

  const selectedAreaText = computed(() => {
    if (selectedArea.value && selectedArea.value.length > 0) {
      // 使用JeecgBoot的区域数据工具来获取名称
      const getAreaNames = (codes: string[]): string => {
        const names: string[] = [];
        for (const code of codes) {
          const area = regionData.find((item) => item.value === code);
          if (area) {
            names.push(area.label);
          }
        }
        return names.join(' / ');
      };

      return getAreaNames(selectedArea.value);
    }
    return '';
  });

  // 更新时间
  const updateTime = () => {
    currentTime.value = dayjs().format('上午HH:mm YYYY年MM月DD日 dddd');
  };

  // 获取物资类型树形数据
  const fetchMaterialTypeTree = async () => {
    try {
      const result = await getMaterialTree();
      console.log('获取物资类型树形数据:', result);
      if (result && Array.isArray(result)) {
        materialTypeOptions.value = result;
      }
    } catch (error) {
      console.error('获取物资类型失败:', error);
    }
  };

  // 物资类型搜索过滤
  const filterMaterialType = (inputValue: string, path: any[]) => {
    return path.some((option) => option.name.toLowerCase().includes(inputValue.toLowerCase()));
  };

  // 物资选择变化处理
  const handleMaterialChange = (value: string[]) => {
    selectedMaterial.value = value;
    console.log('选中的物资类型:', value);
    // 发射物资变化事件
    emit('materialChange', value, selectedMaterialText.value);
  };

  // 省市区选择变化处理
  const handleAreaChange = (value: string[]) => {
    selectedArea.value = value;
    console.log('选中的省市区:', value);
    // 发射省市区变化事件
    emit('areaChange', value, selectedAreaText.value);
  };

  let timeInterval: NodeJS.Timeout;

  onMounted(() => {
    updateTime();
    // 每秒更新时间
    timeInterval = setInterval(updateTime, 1000);
    // 获取物资类型数据
    fetchMaterialTypeTree();
  });

  onUnmounted(() => {
    if (timeInterval) {
      clearInterval(timeInterval);
    }
  });
</script>

<style lang="less" scoped>
  @import '../styles/fullScreen/VisualHeader.less';
  @import '../styles/notFullScreen/VisualHeader.less';
</style>

<style lang="less">
  // 只针对可视化页面的级联选择器下拉面板
  .visual-cascader-dropdown {
    // 展开图标颜色
    .ant-cascader-menu-item-expand-icon {
      color: #fff !important;
    }

    // 选项文字颜色
    .ant-cascader-menu-item {
      color: #fff !important;

      &:hover {
        background-color: rgba(43, 204, 255, 0.2) !important;
      }

      &.ant-cascader-menu-item-active {
        background-color: rgba(43, 204, 255, 0.3) !important;
      }
    }
  }
</style>
