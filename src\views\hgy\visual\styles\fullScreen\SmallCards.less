.small-cards.fullscreen {
  height: 100%;
  padding: 20px;

  .cards-row {
    display: flex;
    height: 100%;
    gap: 20px;
  }

  .cards-column {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 16px;
  }

  .small-card {
    display: flex;
    align-items: center;
    width: 246px;
    height: 50px;
    padding: 3px 0;
    background: linear-gradient(90deg, rgba(9, 189, 228, 0.1) 0%, rgba(9, 189, 228, 0.5) 100%);

    border-radius: 10px;
    gap: 10px;
    transition: all 0.3s ease;
    cursor: pointer;

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 8px 25px rgba(18, 230, 219, 0.3);
      border-color: rgba(18, 230, 219, 0.5);
    }
  }

  .card-icon {
    width: 48px;
    height: 48px;

    .icon-bg {
      width: 100%;
      height: 100%;
      background: url('@/assets/visual/center/icon-bg.png');
      background-size: cover;
      display: flex;
      align-items: center;
      justify-content: center;
      padding-top: 3px;
      padding-right: 1px;
    }
  }

  .card-content {
    flex: 1;
    min-width: 0;
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  .card-label {
    font-size: 16px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    color: #2bccff !important;
  }

  .card-value {
    display: flex;
    align-items: baseline;
    gap: 5px;
    margin-right: 14px;

    :deep(.count-to) {
      color: #fff;
      font-size: 22px;
      font-family: 'DIN Regular';
    }

    .card-unit {
      color: #ffffff;
      font-size: 12px;
    }
  }
}
