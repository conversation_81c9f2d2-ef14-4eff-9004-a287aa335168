<template>
  <div class="main-kpi-cards">
    <!-- 背景 -->
    <div class="cards-bg"></div>

    <!-- 内容 -->
    <div class="cards-content">
      <!-- 成交总额 -->
      <div class="kpi-card">
        <div class="card-label">成交总额</div>
        <div class="card-value">
          <CountTo :start="0" :end="59645956" :duration="2000" :decimals="0" />
          <span class="card-unit">万</span>
        </div>
      </div>

      <div class="kpi-group">
        <!-- 溢价总额 -->
        <div class="kpi-card">
          <div class="card-label" style="font-size: 16px">溢价总额</div>
          <div class="card-value">
            <CountTo :start="0" :end="5956" :duration="2000" :decimals="0" style="font-size: 20px" />
            <span class="card-unit">万</span>
          </div>
        </div>

        <!-- 总溢价率 -->
        <div class="kpi-card" style="margin-left: 10px">
          <div class="card-label" style="font-size: 16px">总溢价率</div>
          <div class="card-value">
            <CountTo :start="0" :end="95.0" :duration="2000" :decimals="2" style="font-size: 20px" />
            <span class="card-unit">%</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  import CountTo from './CountTo.vue';
</script>

<style lang="less" scoped>
  @import '../styles/fullScreen/MainKPICards.less';
  @import '../styles/notFullScreen/MainKPICards.less';
</style>
