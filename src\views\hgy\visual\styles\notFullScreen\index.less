// 非全屏状态下的样式 - 1658px × 821px
.visual-container:not(.fullscreen) {
  position: relative;
  width: 100%;
  height: 100%;
  min-height: calc(100vh - 86px); // 减去新的顶栏高度
  overflow: hidden;
  background: @bg-dark;
  display: flex;
  align-items: center;
  justify-content: center;

  &.fullscreen {
    // 全屏状态下保持正常布局，不隐藏侧边栏和顶栏
    // 只是标记状态，实际布局不变
    position: relative;
  }

  // 添加星空背景效果
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background:
      radial-gradient(2px 2px at 20px 30px, @primary-color, transparent),
      radial-gradient(2px 2px at 40px 70px, fade(@primary-color, 80%), transparent),
      radial-gradient(1px 1px at 90px 40px, fade(@primary-color, 60%), transparent),
      radial-gradient(1px 1px at 130px 80px, fade(@primary-color, 40%), transparent),
      radial-gradient(2px 2px at 160px 30px, fade(@primary-color, 70%), transparent);
    background-repeat: repeat;
    background-size: 200px 100px;
    animation: twinkle 4s ease-in-out infinite alternate;
    z-index: 0;
  }
  @keyframes twinkle {
    0% {
      opacity: 0.3;
    }
    100% {
      opacity: 0.8;
    }
  }

  .visual-bg {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: url('https://huigupaimai.oss-cn-beijing.aliyuncs.com/temp/visual-bg_1755478111715.png') no-repeat center center;
    background-size: cover;
    z-index: 1;

    // 添加渐变遮罩
    &::after {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: linear-gradient(135deg, fade(@bg-dark, 20%) 0%, transparent 30%, transparent 70%, fade(@bg-dark, 20%) 100%);
    }
  }

  .visual-content {
    position: absolute;
    width: 1658px;
    height: 821px;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 2;
    transform-origin: center center;
    transition: transform 0.3s ease;
    display: flex;
    flex-direction: column;
  }

  .visual-header {
    height: 68px;
    width: 100%;
  }

  .visual-middle {
    display: flex;
    justify-content: space-between;
    width: 100%;
    height: 520px;
    padding: 0 15px;
  }

  .visual-left {
    height: 520px;
    z-index: 1; /* 左侧区域在下层 */
    position: relative;
  }

  .visual-center {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    left: 0;
    width: 100%;
    gap: 20px;
    height: 520px;
    z-index: 3; /* 中间区域在上层，压在两侧背景图上方 */
    position: absolute;
    margin: 0 auto;
  }

  .center-top {
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .center-bottom {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .visual-right {
    height: 520px;
    z-index: 1; /* 右侧区域在下层 */
    position: relative;
  }

  .visual-footer {
    height: 277px;
    padding: 12px 20px;
  }
}
