// 非全屏状态下的样式 - 1658px × 821px
.visual-container:not(.fullscreen) .bottom-charts {
  display: flex;
  height: 241px; /* PostCSS 转 vw */
  gap: 20px; /* PostCSS 转 vw */

  .chart-card {
    position: relative;
    flex: 1;
    border-radius: 8px;
    overflow: hidden;
  }

  .card-bg {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: url('https://huigupaimai.oss-cn-beijing.aliyuncs.com/temp/bottom-bg_1754987599965.png') no-repeat center center;
    // background: url('https://huigupaimai.oss-cn-beijing.aliyuncs.com/temp/bottom-bg-noborder_1755745457595.png') no-repeat center center;
    background-size: cover;
    z-index: 1;
  }

  .card-content {
    position: relative;
    height: 100%;
    padding: 2px 15px; /* 转 vw */
    z-index: 2;
  }

  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .card-title {
    color: #fff;
    font-size: 18px; /* 转 vw */
    font-family: 'YouSheBiaoTiHei';
  }

  .card-controls {
    display: flex;
    align-items: center;
    gap: 20px; /* 转 vw */
  }

  .chart-legend {
    display: flex;
    align-items: center;
    gap: 16px; /* 转 vw */
  }

  .legend-item {
    display: flex;
    align-items: center;
    gap: 6px; /* 转 vw */
  }

  .legend-icon {
    flex-shrink: 0;
  }

  .line-icon {
    position: relative;
    width: 12px; /* 转 vw */
    height: 10px; /* 转 vw */
    background: transparent;

    &::before {
      content: '';
      position: absolute;
      top: 50%;
      left: 0;
      width: 12px; /* 转 vw */
      height: 2px; /* 转 vw */
      background: #2bccff;
      transform: translateY(-50%);
    }

    &::after {
      content: '';
      position: absolute;
      top: 50%;
      left: 50%;
      width: 6px; /* 转 vw */
      height: 6px; /* 转 vw */
      background: #ffffff;
      border: 2px solid #2bccff;
      border-radius: 50%;
      transform: translate(-50%, -50%);
    }
  }

  .bar-icon {
    width: 28px; /* 转 vw */
    height: 11px; /* 转 vw */
    background: #2bccff;
    border-radius: 2px;
  }

  .legend-text {
    color: #ffffff;
    font-size: 12px; /* 转 vw */
    font-weight: 400;
  }

  .chart-filter {
    position: relative;
    width: 68px; /* 转 vw */
    height: 23px; /* 转 vw */
    cursor: pointer;
    transition: all 0.3s ease;

    background: linear-gradient(135deg, #2bccff 0%, #1a8fcc 50%, #2bccff 100%);

    clip-path: polygon(0 0, ~'calc(100% - 10px)' 0, 100% 10px, 100% 100%, 10px 100%, 0 ~'calc(100% - 10px)');

    /* 外发光效果 */
    box-shadow:
      0 0 6px rgba(19, 230, 219, 0.4),
      0 0 12px rgba(19, 230, 219, 0.2);

    /* 添加发光动画 */
    animation: buttonGlow 3s ease-in-out infinite;

    &:hover {
      transform: translateY(-1px);
      box-shadow:
        0 0 10px rgba(19, 230, 219, 0.6),
        0 0 16px rgba(19, 230, 219, 0.3);

      .filter-select {
        background: linear-gradient(135deg, rgba(0, 76, 102, 0.9) 0%, rgba(0, 60, 80, 1) 50%, rgba(0, 40, 60, 0.9) 100%);

        box-shadow:
          inset 0 0 10px rgba(19, 230, 219, 0.4),
          inset 0 0 16px rgba(19, 230, 219, 0.2);
      }
    }

    // 下拉箭头
    &::after {
      content: '';
      position: absolute;
      top: 50%;
      right: 12px;
      width: 0;
      height: 0;
      border-left: 3px solid transparent;
      border-right: 3px solid transparent;
      border-top: 3px solid #ffffff;
      transform: translateY(-50%);
      pointer-events: none;
      z-index: 3;
    }
  }

  .filter-select {
    position: absolute;
    top: 2px;
    left: 2px;
    right: 2px;
    bottom: 2px;
    appearance: none;
    border: none;
    outline: none;

    /* 内部容器背景 */
    background: linear-gradient(135deg, rgba(0, 76, 102, 0.8) 0%, rgba(0, 60, 80, 0.9) 50%, rgba(0, 40, 60, 0.8) 100%);

    /* 内部容器的切角效果（比外部小2px） */
    clip-path: polygon(0 0, ~'calc(100% - 8px)' 0, 100% 8px, 100% 100%, 8px 100%, 0 ~'calc(100% - 8px)');

    /* 内发光效果 */
    box-shadow:
      inset 0 0 6px rgba(19, 230, 219, 0.3),
      inset 0 0 12px rgba(19, 230, 219, 0.1);

    /* 文字内容样式 */
    color: #ffffff;
    font-size: 11px; /* 转 vw */
    padding: 0 20px 0 10px; /* 转 vw */
    cursor: pointer;
    text-align: left;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;

    // 优化选项样式
    option {
      background: rgba(0, 76, 102, 0.9);
      color: #2bccff;
      padding: 6px 12px; /* 转 vw */
      border: none;
      font-size: 11px; /* 转 vw */
      font-weight: 400;

      &:hover {
        background: rgba(0, 96, 122, 0.9);
      }

      &:checked {
        background: #2bccff;
        color: #ffffff;
      }
    }
  }

  .ranking-grid {
    height: calc(100% - 40px);
    display: flex;
    flex-direction: column;
  }

  .top-row,
  .bottom-row {
    display: flex;
    justify-content: space-around;
    flex: 1;
  }

  .ranking-medal {
    position: relative;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;
    width: 146px; /* 转 vw */
    height: 100px; /* 转 vw */
  }

  .medal-bg-container {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: url('@/assets/visual/center/cj-bg.png') no-repeat center center;
    background-size: contain;
    z-index: 1;
  }

  .medal-content {
    position: relative;
    display: flex;
    flex-direction: column;
    align-items: center;
    z-index: 2;
    height: 100%;
    justify-content: center;
    gap: 12px;
  }

  .medal-icon {
    position: relative;
    width: 50.6px; /* 转 vw */
    height: 29px; /* 转 vw */
    flex-shrink: 0;
    margin-top: 5px; /* 转 vw */

    .medal-bg {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background-size: contain;
      background-repeat: no-repeat;
      background-position: center;
      background-image: url('@/assets/visual/medal-regular.png');
    }

    .rank-number {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      color: #004c66 !important;
      font-size: 11px;
      z-index: 2;
      font-family: 'DIN Bold';
    }
  }

  .ranking-medal.rank-1 .medal-icon .medal-bg {
    background-image: url('@/assets/visual/medal-gold.png');
  }

  .ranking-medal.rank-2 .medal-icon .medal-bg {
    background-image: url('@/assets/visual/medal-silver.png');
  }

  .ranking-medal.rank-3 .medal-icon .medal-bg {
    background-image: url('@/assets/visual/medal-cuprum.png');
  }

  .medal-info {
    text-align: center;
  }

  .medal-value {
    color: #fff;
    font-size: 20px; /* 转 vw */
    text-shadow: 0 0 8px rgba(18, 230, 219, 0.5);
    font-family: 'DIN Regular';
    .medal-unit {
      font-size: 12px; /* 转 vw */
      font-family: 'PingFang Medium';
    }
  }

  .medal-name {
    color: #ffffff;
    font-size: 14px; /* 转 vw */
    opacity: 0.8;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 110px; /* 转 vw */
  }

  .chart-wrapper {
    height: calc(100% - 40px);
  }

  .chart {
    width: 100%;
    height: 100%;
  }

  /* 发光动画效果 */
  @keyframes buttonGlow {
    0% {
      box-shadow:
        0 0 6px rgba(43, 204, 255, 0.4),
        0 0 12px rgba(43, 204, 255, 0.2);
    }
    50% {
      box-shadow:
        0 0 10px rgba(43, 204, 255, 0.6),
        0 0 16px rgba(43, 204, 255, 0.3);
    }
    100% {
      box-shadow:
        0 0 6px rgba(43, 204, 255, 0.4),
        0 0 12px rgba(43, 204, 255, 0.2);
    }
  }
}
